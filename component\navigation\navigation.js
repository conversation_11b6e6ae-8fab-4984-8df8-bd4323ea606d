// 导航组件加载器
(function () {
	// 获取正确的基础路径
	function getBasePath() {
		const currentPath = window.location.pathname;
		if (currentPath.includes("/assets/")) {
			return "../../";
		}
		return "./";
	}

	// 加载导航组件
	function loadNavigation() {
		const basePath = getBasePath();
		fetch(basePath + "component/navigation/navigation.html")
			.then((response) => response.text())
			.then((html) => {
				const navigationContainer = document.getElementById(
					"navigation-container"
				);
				if (navigationContainer) {
					// 修复HTML中的路径
					html = fixNavigationPaths(html, basePath);
					navigationContainer.innerHTML = html;
					// 导航加载完成后初始化交互功能
					initNavigationInteractions();
				}
			})
			.catch((error) => {
				console.error("导航组件加载失败:", error);
			});
	}

	// 修复导航HTML中的路径
	function fixNavigationPaths(html, basePath) {
		// 修复所有相对路径
		html = html.replace(/href="\.\/([^"]+)"/g, `href="${basePath}$1"`);
		html = html.replace(
			/onclick="window\.location\.href='\.\/([^']+)'/g,
			`onclick="window.location.href='${basePath}$1'`
		);
		return html;
	}

	// 初始化导航交互功能
	function initNavigationInteractions() {
		// 高亮当前页面对应的导航链接
		highlightCurrentPage();

		// 添加导航链接悬停效果
		addHoverEffects();
	}

	// 高亮当前页面对应的导航链接
	function highlightCurrentPage() {
		const currentPath = window.location.pathname;
		const navLinks = document.querySelectorAll(".nav-link");

		navLinks.forEach((link) => {
			const linkPath = link.getAttribute("href");
			if (
				currentPath.includes(linkPath.replace("./", "")) ||
				(currentPath.includes("home") && linkPath.includes("home")) ||
				(currentPath.includes("aboutus") && linkPath.includes("aboutus")) ||
				(currentPath.includes("recruitment") &&
					linkPath.includes("recruitment")) ||
				(currentPath.includes("contact") && linkPath.includes("contact")) ||
				(currentPath.includes("preview") && linkPath.includes("preview")) ||
				(currentPath.includes("detail") && linkPath.includes("detail"))
			) {
				link.classList.add("active");
			}
		});
	}

	// 添加悬停效果
	function addHoverEffects() {
		const navLinks = document.querySelectorAll(".nav-link, .product-link");

		navLinks.forEach((link) => {
			link.addEventListener("mouseenter", function () {
				this.style.transform = "translateY(-2px)";
				this.style.transition = "transform 0.3s ease";
			});

			link.addEventListener("mouseleave", function () {
				this.style.transform = "translateY(0)";
			});
		});
	}

	// 页面加载完成后执行
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", loadNavigation);
	} else {
		loadNavigation();
	}
})();
