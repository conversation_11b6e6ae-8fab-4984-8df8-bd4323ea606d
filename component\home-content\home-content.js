// 主页内容组件加载器
(function () {
	// 加载主页内容组件
	function loadHomeContent() {
		fetch("./component/home-content/home-content.html")
			.then((response) => response.text())
			.then((html) => {
				const homeContentContainer = document.getElementById(
					"home-content-container"
				);
				if (homeContentContainer) {
					homeContentContainer.innerHTML = html;
					// 组件加载完成后初始化功能
					initHomeContentFeatures();
				}
			})
			.catch((error) => {
				console.error("主页内容组件加载失败:", error);
			});
	}

	// 初始化主页内容功能
	function initHomeContentFeatures() {
		// 新闻卡片悬停效果
		const newsCards = document.querySelectorAll(".news-card");
		newsCards.forEach((card) => {
			card.addEventListener("mouseenter", function () {
				this.style.transform = "translateY(-4px)";
			});

			card.addEventListener("mouseleave", function () {
				this.style.transform = "translateY(0)";
			});
		});

		// 解决方案按钮点击事件
		const solutionsBtn = document.querySelector(".solutions-btn");
		if (solutionsBtn) {
			solutionsBtn.addEventListener("click", function () {
				// 跳转到解决方案页面（产品预览页面）
				window.location.href = "./preview.html";
			});
		}

		// 浏览全部新闻链接点击事件
		const browseAllLink = document.querySelector(".browse-all-link");
		if (browseAllLink) {
			browseAllLink.addEventListener("click", function (e) {
				e.preventDefault();
				// 跳转到新闻详情页面
				window.location.href = "./detail.html";
			});
		}

		// 新闻卡片点击事件
		newsCards.forEach((card, index) => {
			card.addEventListener("click", function () {
				// 跳转到新闻详情页面
				window.location.href = "./detail.html";
			});
		});
	}

	// 页面加载完成后执行
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", loadHomeContent);
	} else {
		loadHomeContent();
	}
})();
