/* 基础样式重置和全局设置 */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: "AlibabaPuHuiTi-Regular", "PingFang SC", "Microsoft YaHei",
		sans-serif;
	line-height: 1.6;
	color: #333;
	background-color: #fff;
}

.page {
	width: 100%;
	min-height: 100vh;
}

.container {
	max-width: 1030px;
	margin: 0 auto;
	padding: 0;
}

/* 英雄区域样式 */
.hero-section {
	background: url(./img/SketchPngc22cd3bfbdf2b4c82b16fe88718024391d5b03af75c1b89a6da888553145132a.png)
		center/cover no-repeat;
	height: 520px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	color: #fff;
}

.hero-content {
	text-align: center;
}

.hero-title {
	font-size: 48px;
	font-weight: normal;
	margin-bottom: 10px;
	line-height: 1.5;
}

.hero-subtitle {
	font-size: 24px;
	font-weight: 300;
	margin: 0;
}

/* 主要内容区域样式 */
.main-content {
	background-color: #fff;
}

/* 公司介绍区域 */
.company-intro {
	padding: 60px 0;
	background-color: #fff;
}

.intro-text {
	font-size: 24px;
	font-weight: 300;
	line-height: 1.4;
	text-align: center;
	margin-bottom: 60px;
	max-width: 900px;
	margin-left: auto;
	margin-right: auto;
}

/* 公司宗旨标题区域 */
.purpose-title-section {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.section-title {
	font-size: 36px;
	font-weight: normal;
	text-align: center;
	color: #333;
	position: relative;
	z-index: 2;
}
.section-title-- {
	font-size: 36px;
	font-weight: normal;
	color: #333;
	position: relative;
	margin-bottom: 20px;
}

.title-decoration-line {
	position: absolute;
	top: 55%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: auto;
	height: auto;
	max-width: 400px;
	z-index: 1;
}

/* 我们的宗旨内容区域 */
.purpose-content-section {
	background: url(./img/b8d447e677534c7b957e2e9fc11992af_mergeImage.png)
		center/contain no-repeat;
	border-radius: 8px;
	position: relative;
	min-height: 500px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 -25px;
}

.purpose-text-content {
	padding: 80px 65px 80px 0;
	max-width: 625px;
	margin-left: auto;
}

.purpose-subtitle {
	font-size: 36px;
	font-weight: normal;
	margin-bottom: 20px;
	color: #333;
}

.purpose-description {
	font-size: 16px;
	line-height: 1.6;
	color: #333;
	text-align: justify;
}

/* 业务板块区域 */
.business-section {
	padding: 80px 0;
	background-color: #f8f9fa;
}

.business-header {
	text-align: center;
	margin-bottom: 60px;
}

/* 我们的业务标题区域 */
.business-title-section {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 5px;
}

.section-description {
	font-size: 16px;
	line-height: 1.6;
	color: #666;
	max-width: 600px;
	margin: 0 auto;
}

.business-cards {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 24px;
	margin-top: 40px;
}

.business-card {
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 12px rgba(86, 86, 86, 0.1);
	overflow: hidden;
	padding: 16px 20px;
	transition: all 0.3s ease;
}

.business-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 8px 24px rgba(86, 86, 86, 0.15);
}

.card-image {
	height: 200px;
	overflow: hidden;
}

.card-image img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.3s ease;
}

.business-card:hover .card-image img {
	transform: scale(1.05);
}

.card-content {
	margin-top: 18px;
}

.card-title {
	font-size: 18px;
	font-weight: normal;
	margin-bottom: 16px;
	color: #333;
}

.card-description {
	font-size: 14px;
	line-height: 1.6;
	color: #666;
}

/* 视频区域 */
.video-section {
	padding: 60px 0;
	background-color: #fff;
}

.video-header {
	display: flex;
	align-items: center;
	gap: 12px;
	width: 860px;
	margin: 0 auto 12px;
}

.video-label {
	font-size: 14px;
	color: #333;
}

.video-arrow {
	width: 7px;
	height: 14px;
	background-image: url(./img/SketchPnge5e249ccce36abb7acdfb3d406d18f27632f85fc6421796ca49dcdde4b914dda.png);
	background-size: contain;
	background-repeat: no-repeat;
}

.video-container {
	max-width: 860px;
	margin: 0 auto;
}

.video-thumbnail {
	position: relative;
	border-radius: 8px;
	overflow: hidden;
	cursor: pointer;
}

.video-thumbnail img {
	width: 100%;
	height: 500px;
	object-fit: cover;
}

.play-button {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border: none;
	border-radius: 50%;
	width: 100px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.play-button:hover {
	transform: translate(-50%, -50%) scale(1.1);
}

.play-button img {
	width: 100px;
	height: 100px;
}

/* 战略区域 */
.strategy-section {
	padding: 80px 0;
	background: linear-gradient(270deg, #dfdddd 0%, #f4f4f4 100%);
}

/* 战略标题包装器 */
.strategy-title-wrapper {
	position: relative;
	margin-bottom: 20px;
}

/* 战略装饰条 */
.strategy-decoration-line {
	display: block;
	width: auto;
	height: auto;
	max-width: 400px;
	margin-bottom: 0px;
}
.strategy-text {
	padding-top: 36px;
}
.strategy-content {
	display: grid;
	grid-template-columns: 1fr 360px;
	gap: 40px;
	background: #fff;
	border-radius: 8px;
	padding: 8px 40px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.strategy-title {
	font-size: 36px;
	line-height: 36px;
	font-weight: normal;
	margin-bottom: 20px;
	color: #333;
}

.strategy-description {
	font-size: 16px;
	line-height: 22px;
	color: #333;
	text-align: justify;
}

.strategy-image img {
	width: 100%;
	height: auto;
	border-radius: 8px;
}

/* 多元化与包容性区域 */
.diversity-section {
	padding: 80px 0;
	background: url(./img/89c9827e71bd47e995825799d1464712_mergeImage.png)
		center/cover no-repeat;
}

.diversity-content {
	background: rgba(255, 255, 255, 0.95);
	box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.25);
	border-radius: 8px;
	padding: 60px;
	max-width: 920px;
	margin: 22px auto 0;
}

.diversity-description {
	font-size: 16px;
	line-height: 1.6;
	color: #333;
	text-align: justify;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.container {
		max-width: 960px;
	}

	.hero-title {
		font-size: 40px;
	}

	.hero-subtitle {
		font-size: 20px;
	}
}

@media (max-width: 900px) {
	.container {
		max-width: 720px;
		padding: 0 16px;
	}

	.hero-title {
		font-size: 36px;
	}

	.hero-subtitle {
		font-size: 18px;
	}

	.purpose-content-section {
		min-height: 400px;
		padding: 20px;
	}

	.purpose-text-content {
		padding: 30px 20px;
		max-width: 100%;
	}

	.business-cards {
		grid-template-columns: 1fr;
		gap: 20px;
	}

	.strategy-content {
		grid-template-columns: 1fr;
		gap: 30px;
	}

	.strategy-image {
		order: -1;
	}
}

@media (max-width: 600px) {
	.container {
		padding: 0 12px;
	}

	.hero-section {
		height: 400px;
		padding: 0 12px;
	}

	.hero-title {
		font-size: 28px;
	}

	.hero-subtitle {
		font-size: 16px;
	}

	.section-title {
		font-size: 28px;
	}

	.intro-text {
		font-size: 18px;
	}

	.title-decoration-line {
		max-width: 280px;
	}

	.section-title {
		font-size: 28px;
		padding: 0 15px;
	}

	.purpose-content-section {
		min-height: 350px;
		padding: 15px;
	}

	.purpose-text-content {
		padding: 25px 15px;
		max-width: 100%;
	}

	.purpose-subtitle {
		font-size: 24px;
	}

	.company-intro,
	.business-section,
	.video-section,
	.strategy-section,
	.diversity-section {
		padding: 40px 0;
	}

	.diversity-content {
		padding: 30px 20px;
	}

	.strategy-content {
		padding: 30px 20px;
	}

	.strategy-title {
		font-size: 24px;
	}

	.video-thumbnail img {
		height: 300px;
	}

	.play-button {
		width: 80px;
		height: 80px;
	}

	.play-button img {
		width: 40px;
		height: 40px;
	}
}

/* 额外的交互效果和动画 */
.business-card,
.play-button {
	transition: all 0.3s ease;
}

.business-card:hover {
	transform: translateY(-4px);
}

.play-button:hover {
	transform: translate(-50%, -50%) scale(1.1);
}

/* 确保图片响应式 */
img {
	max-width: 100%;
	height: auto;
}

/* 文本选择样式 */
::selection {
	background-color: #0066cc;
	color: #fff;
}

/* 滚动条样式 */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
	background: #888;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #555;
}
