// 产品预览功能
class ProductPreview {
	constructor() {
		this.products = window.productsData || []
		this.currentProductIndex = 0
		this.currentThumbnailIndex = 0
		this.isAnimating = false
		this.init()
	}

	init() {
		if (this.products.length === 0) {
			console.warn("No products data found")
			return
		}
		this.bindEvents()
		this.updateDisplay()
	}

	bindEvents() {
		// 左右箭头点击事件
		const leftArrow = document.querySelector(".nav-arrow-left")
		const rightArrow = document.querySelector(".nav-arrow-right")

		if (leftArrow) {
			leftArrow.addEventListener("click", () => {
				if (!this.isAnimating) {
					this.switchProduct("prev")
				}
			})
		}

		if (rightArrow) {
			rightArrow.addEventListener("click", () => {
				if (!this.isAnimating) {
					this.switchProduct("next")
				}
			})
		}

		// 产品分类点击事件
		document.querySelectorAll(".category-link").forEach((link, index) => {
			link.addEventListener("click", e => {
				e.preventDefault()
				if (!this.isAnimating && index !== this.currentProductIndex) {
					this.switchToProduct(index)
				}
			})
		})

		// 缩略图点击事件
		document.addEventListener("click", e => {
			if (e.target.classList.contains("product-thumbnail")) {
				const thumbnailIndex = parseInt(e.target.dataset.thumbnailIndex)
				if (
					!isNaN(thumbnailIndex) &&
					thumbnailIndex !== this.currentThumbnailIndex
				) {
					this.switchThumbnail(thumbnailIndex)
				}
			}
		})

		// 配置标签点击事件
		document.querySelectorAll(".config-tab").forEach((tab, index) => {
			tab.addEventListener("click", () => {
				this.switchConfigTab(index)
			})
		})

		// 键盘导航支持
		document.addEventListener("keydown", e => {
			if (e.key === "ArrowLeft" && !this.isAnimating) {
				this.switchProduct("prev")
			} else if (e.key === "ArrowRight" && !this.isAnimating) {
				this.switchProduct("next")
			}
		})
	}

	switchProduct(direction) {
		const totalProducts = this.products.length

		if (direction === "next") {
			this.currentProductIndex = (this.currentProductIndex + 1) % totalProducts
		} else {
			this.currentProductIndex =
				(this.currentProductIndex - 1 + totalProducts) % totalProducts
		}

		this.currentThumbnailIndex = 0 // 重置缩略图索引
		this.updateDisplay(true)
	}

	switchToProduct(index) {
		if (index >= 0 && index < this.products.length) {
			this.currentProductIndex = index
			this.currentThumbnailIndex = 0
			this.updateDisplay(true)
		}
	}

	switchThumbnail(index) {
		const currentProduct = this.products[this.currentProductIndex]
		if (index >= 0 && index < currentProduct.thumbnails.length) {
			this.currentThumbnailIndex = index
			this.updateMainImage()
			this.updateThumbnailActive()
		}
	}

	switchConfigTab(index) {
		document.querySelectorAll(".config-tab").forEach((tab, i) => {
			tab.classList.toggle("active", i === index)
		})
	}

	updateDisplay(withAnimation = false) {
		if (withAnimation) {
			this.isAnimating = true
			const content = document.querySelector(".product-preview-content")
			content.classList.add("switching")

			setTimeout(() => {
				this.performUpdate()
				content.classList.remove("switching")
				this.isAnimating = false
			}, 150)
		} else {
			this.performUpdate()
		}
	}

	performUpdate() {
		this.updateCategoryActive()
		this.updateProductImages()
		this.updateProductSpecs()
		this.updateMainImage()
		this.updateThumbnailActive()
	}

	updateCategoryActive() {
		document.querySelectorAll(".category-link").forEach((link, index) => {
			link.classList.toggle("active", index === this.currentProductIndex)
		})
	}

	updateProductImages() {
		const currentProduct = this.products[this.currentProductIndex]
		const thumbnailsContainer = document.querySelector(".product-thumbnails")

		if (!thumbnailsContainer || !currentProduct) return

		// 更新缩略图
		thumbnailsContainer.innerHTML = ""
		currentProduct.thumbnails.forEach((thumbnail, index) => {
			const thumbnailHtml = `
                <div class="product-image-item">
                    <img src="${thumbnail}"
                         alt="产品预览${index + 1}"
                         class="product-thumbnail ${
														index === 0 ? "active" : ""
													}"
                         data-thumbnail-index="${index}" />
                </div>
            `
			thumbnailsContainer.insertAdjacentHTML("beforeend", thumbnailHtml)
		})
	}

	updateMainImage() {
		const currentProduct = this.products[this.currentProductIndex]
		const mainImg = document.getElementById("main-product-img")

		if (!mainImg || !currentProduct) return

		// 使用当前选中的缩略图，如果没有则使用主图
		if (currentProduct.thumbnails[this.currentThumbnailIndex]) {
			mainImg.src = currentProduct.thumbnails[this.currentThumbnailIndex]
		} else {
			mainImg.src = currentProduct.main_image
		}

		// 更新alt属性
		mainImg.alt = `${currentProduct.name} - 产品图片${
			this.currentThumbnailIndex + 1
		}`
	}

	updateThumbnailActive() {
		document.querySelectorAll(".product-thumbnail").forEach((thumb, index) => {
			thumb.classList.toggle("active", index === this.currentThumbnailIndex)
		})
	}

	updateProductSpecs() {
		const currentProduct = this.products[this.currentProductIndex]
		const specsContainer = document.getElementById("product-specs")

		if (!specsContainer || !currentProduct || !currentProduct.specs) return

		specsContainer.innerHTML = ""
		currentProduct.specs.forEach((spec, index) => {
			const specHtml = `
                <div class="spec-item">
                    <span class="spec-value">${this.escapeHtml(
											spec.value
										)}</span>
                    <span class="spec-label">${this.escapeHtml(
											spec.label
										)}</span>
                </div>
                ${
									index < currentProduct.specs.length - 1
										? '<div class="spec-divider"></div>'
										: ""
								}
            `
			specsContainer.insertAdjacentHTML("beforeend", specHtml)
		})
	}

	// 工具方法：HTML转义
	escapeHtml(text) {
		const map = {
			"&": "&amp;",
			"<": "&lt;",
			">": "&gt;",
			'"': "&quot;",
			"'": "&#039;"
		}
		return text.replace(/[&<>"']/g, function (m) {
			return map[m]
		})
	}

	// 获取当前产品信息
	getCurrentProduct() {
		return this.products[this.currentProductIndex]
	}

	// 获取产品总数
	getTotalProducts() {
		return this.products.length
	}
}

// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", () => {
	// 初始化产品预览功能
	window.productPreview = new ProductPreview()

	// 可选：添加全局方法供外部调用
	window.switchToProduct = index => {
		if (window.productPreview) {
			window.productPreview.switchToProduct(index)
		}
	}

	window.getCurrentProduct = () => {
		return window.productPreview
			? window.productPreview.getCurrentProduct()
			: null
	}
})
