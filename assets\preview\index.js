// 产品预览区域交互功能
class ProductPreviewInteraction {
	constructor() {
		this.init();
	}

	init() {
		this.bindEvents();
		this.addAccessibilityFeatures();
	}

	bindEvents() {
		// 为所有"了解更多"按钮添加点击事件
		const learnMoreButtons = document.querySelectorAll(".learn-more-btn");
		learnMoreButtons.forEach((button) => {
			button.addEventListener("click", this.handleLearnMoreClick.bind(this));
		});

		// 为产品卡片添加键盘导航支持
		const productCards = document.querySelectorAll(".product-card");
		productCards.forEach((card) => {
			card.addEventListener("keydown", this.handleCardKeydown.bind(this));
		});

		// 为缩略图添加点击事件
		const thumbnailCards = document.querySelectorAll(".product-thumbnail-card");
		thumbnailCards.forEach((card) => {
			card.addEventListener("click", this.handleThumbnailClick.bind(this));
		});
	}

	handleLearnMoreClick(event) {
		event.preventDefault();
		const productCard = event.target.closest(".product-card");
		const productCategory =
			productCard.querySelector(".product-category").textContent;

		// 这里可以添加具体的跳转逻辑或弹窗显示
		console.log(`点击了解更多: ${productCategory}`);

		// 添加视觉反馈
		event.target.style.transform = "scale(0.95)";
		setTimeout(() => {
			event.target.style.transform = "";
		}, 150);
	}

	handleCardKeydown(event) {
		// 支持Enter键和空格键激活卡片
		if (event.key === "Enter" || event.key === " ") {
			event.preventDefault();
			const learnMoreBtn = event.target.querySelector(".learn-more-btn");
			if (learnMoreBtn) {
				learnMoreBtn.click();
			}
		}
	}

	handleThumbnailClick(event) {
		const thumbnailLabel =
			event.currentTarget.querySelector(".thumbnail-label").textContent;
		console.log(`点击缩略图: ${thumbnailLabel}`);

		// 添加点击动画效果
		const thumbnailImage =
			event.currentTarget.querySelector(".thumbnail-image");
		thumbnailImage.style.transform = "scale(1.2)";
		setTimeout(() => {
			thumbnailImage.style.transform = "";
		}, 200);
	}

	addAccessibilityFeatures() {
		// 为产品卡片添加tabindex，支持键盘导航
		const productCards = document.querySelectorAll(".product-card");
		productCards.forEach((card, index) => {
			card.setAttribute("tabindex", "0");
			card.setAttribute("role", "button");
			card.setAttribute("aria-label", `产品卡片 ${index + 1}`);
		});

		// 为缩略图添加可访问性属性
		const thumbnailCards = document.querySelectorAll(".product-thumbnail-card");
		thumbnailCards.forEach((card, index) => {
			card.setAttribute("tabindex", "0");
			card.setAttribute("role", "button");
			card.setAttribute("aria-label", `产品缩略图 ${index + 1}`);
		});
	}
}

// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", () => {
	console.log("产品预览区域已加载");
	new ProductPreviewInteraction();
});
